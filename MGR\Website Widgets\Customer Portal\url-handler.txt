add_action('init', function() {
    add_rewrite_rule(
        '^customer-portal/(.*)$',
        'index.php?mgr_redirect=true&path=$1',
        'top'
    );
});

add_filter('query_vars', function($vars) {
    $vars[] = 'mgr_redirect';
    $vars[] = 'path';
    return $vars;
});

add_action('template_redirect', function() {
    if (get_query_var('mgr_redirect')) {
        // Prevent caching
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        // Prevent indexing
        header('X-Robots-Tag: noindex, nofollow');
        
        // Allow framing from My Gadget Repairs
        header('X-Frame-Options: ALLOW-FROM https://www.mygadgetrepairs.com');
        
        // Set specific Content Security Policy for customer portal
        header("Content-Security-Policy: default-src 'self' https://www.mygadgetrepairs.com; script-src 'self' https://www.mygadgetrepairs.com 'unsafe-inline' 'unsafe-eval'; style-src 'self' https://www.mygadgetrepairs.com 'unsafe-inline'; img-src 'self' https://www.mygadgetrepairs.com data:; frame-src https://www.mygadgetrepairs.com");
        
        $base_url = 'https://www.mygadgetrepairs.com/customer/';
        $portal_identifier = '2906ff01bdc0ab0e';
        
        $path = get_query_var('path', '');
        $redirect_url = $base_url . $path . (strpos($path, '?') !== false ? '&' : '?') . "portal=" . $portal_identifier;
        
        wp_redirect($redirect_url);
        exit;
    }
});