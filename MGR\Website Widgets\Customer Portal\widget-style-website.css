/* Elementor-specific reset for portal container */
.elementor-widget-shortcode .mgr-customer-portal-container,
.elementor-section-wrap .mgr-customer-portal-container,
.elementor-section-boxed .mgr-customer-portal-container,
.elementor-container .mgr-customer-portal-container {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Container styling - consolidated */
.elementor-widget-shortcode,
.mgr-customer-portal-container {
    width: 100% !important;
    height: auto !important;
    min-height: 100vh !important;
    overflow: visible !important;
}

/* iframe styling */
.mgr-customer-portal-container iframe {
    width: 100% !important;
    height: 100vh !important;
    border: none !important;
    overflow: auto !important;
    display: block !important;
}

/* Mobile responsive design */
@media screen and (max-width: 768px) {
    .elementor-widget-shortcode,
    .mgr-customer-portal-container {
        min-height: 100vh !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .mgr-customer-portal-container iframe {
        height: 100vh !important;
        width: 100% !important;
        border: none !important;
        border-radius: 0 !important;
    }

    /* Ensure proper touch scrolling on mobile */
    .mgr-customer-portal-container {
        -webkit-overflow-scrolling: touch !important;
        overflow-y: auto !important;
    }
}

@media screen and (max-width: 480px) {
    .elementor-widget-shortcode,
    .mgr-customer-portal-container {
        min-height: 100vh !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .mgr-customer-portal-container iframe {
        height: 100vh !important;
        width: 100% !important;
        border: none !important;
        border-radius: 0 !important;
        /* Optimize for small screens */
        transform: scale(1) !important;
        transform-origin: top left !important;
    }

    /* Enhanced touch scrolling for very small screens */
    .mgr-customer-portal-container {
        -webkit-overflow-scrolling: touch !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }
}

/* Landscape orientation adjustments for mobile */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .mgr-customer-portal-container iframe {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}