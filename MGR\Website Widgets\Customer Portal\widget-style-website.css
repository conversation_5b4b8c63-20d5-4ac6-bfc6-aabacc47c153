/* Elementor reset and base layout */
.elementor-section-wrap,
.elementor-section-boxed,
.elementor-container {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

html,
body {
    height: auto !important;
    min-height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: auto !important;
}

/* Container styling - consolidated */
.elementor-widget-shortcode,
.mgr-customer-portal-container {
    width: 100% !important;
    height: auto !important;
    min-height: 100vh !important;
}

.mgr-customer-portal-container {
    overflow: visible !important;
}

/* Iframe styling */
.mgr-customer-portal-container iframe {
    width: 100% !important;
    height: 100vh !important;
    border: none !important;
    overflow: auto !important;
}

/* Mobile responsive - no changes needed as iframe height remains the same */
@media screen and (max-width: 768px) {
    .mgr-customer-portal-container iframe {
        height: 100vh !important;
    }
}