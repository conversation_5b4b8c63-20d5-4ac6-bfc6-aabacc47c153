/* Reset any Elementor-specific styles that might interfere */
.elementor-section-wrap,
.elementor-section-boxed,
.elementor-container {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Style the shortcode container */
.elementor-widget-shortcode {
    width: 100% !important;
    height: auto !important;
    min-height: 100vh !important;
}

/* Target the specific MGR customer portal output */
.mgr-customer-portal-container {
    width: 100% !important;
    height: auto !important;
    min-height: 100vh !important;
    overflow: visible !important;
}

/* Style the iframe within the MGR customer portal */
.mgr-customer-portal-container iframe {
    width: 100% !important;
    height: 100vh !important; /* Fixed height */
    border: none !important;
    overflow: auto !important; /* Allow scrolling within iframe */
}

/* Ensure the page allows scrolling */
html, body {
    height: auto !important;
    min-height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: auto !important;
}

/* Adjustments for smaller screens */
@media screen and (max-width: 768px) {
    .mgr-customer-portal-container iframe {
        height: 100vh !important; /* Keep fixed height on mobile */
    }
}