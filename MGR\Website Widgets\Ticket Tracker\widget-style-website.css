/* Styling For Ticket Tracking Widget */

/* Font Face Definitions */
@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-regular-webfont.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-semibold-webfont.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-bold-webfont.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

/* Main Container */
#mgr-ticket-container {
  padding: 0 0 10px;
  background: #232729;
  font-family: 'Inter', Arial, sans-serif;
  border-radius: 5px;
}

/* Typography */
#mgr-ticket-container h3 {
  font-size: 2.441rem;
  font-weight: 700;
  color: #f8f8ff;
  padding-bottom: 10px;
  background-color: transparent !important;
}

#mgr-ticket-container small {
  color: #7f8c8d;
  font-size: 1.563rem;
  font-family: 'Inter', Arial, sans-serif !important;
  font-weight: 400 !important;
  background-color: transparent !important;
  display: inline !important;
}

/* Labels - consolidated styling with high specificity */
#mgr-ticket-container label,
#mgr-ticket-container .form-group label,
div#mgr-ticket-container label {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  width: auto !important;
  position: relative !important;
  clip: auto !important;
  overflow: visible !important;
  font-family: 'Inter', Arial, sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #f8f8ff !important;
  background-color: transparent !important;
  margin-bottom: 10px !important;
  padding-bottom: 0 !important;
  line-height: 1.3 !important;
}

/* Specific input labels */
#mgr-ticket-container label[for="mgr_ticket_ref"],
#mgr-ticket-container label[for="mgr_phone_no"] {
  display: block !important;
  margin-bottom: 5px !important;
  font-weight: 600 !important;
}

/* Ticket prefix styling */
#mgr-ticket-container .mgr-prefix-wrapper {
  position: relative;
}

#mgr-ticket-container .mgr-prefix.form-control {
  position: absolute !important;
  background: white !important;
  top: 1px !important;
  left: 2px !important;
  width: auto !important;
  padding: 7px 0 7px 7px !important;
  margin: 0 !important;
  height: 32px !important;
  font-weight: bold !important;
  color: #7f8c8d !important;
  font-size: 1.563rem !important;
  border: 0 !important;
  z-index: 1 !important;
  display: block !important;
  pointer-events: none !important;
}

#mgr-ticket-container #mgr_ticket_ref {
  padding-left: 37.7188px !important;
}

/* Form Fields - consolidated styling */
#mgr-ticket-container input[type="text"],
#mgr-ticket-container input[type="email"],
#mgr-ticket-container input[type="tel"],
#mgr-ticket-container textarea,
#mgr-ticket-container select,
#mgr-ticket-container .form-control:not(.mgr-prefix) {
  width: 100% !important;
  padding: 10px !important;
  margin-bottom: 15px !important;
  border: 1px solid #656565 !important;
  border-radius: 3px !important;
  font-family: 'Inter', Arial, sans-serif !important;
  font-size: 1.563rem !important;
  color: #232729 !important;
  background-color: #f8f8ff !important;
  line-height: 1.5 !important;
  font-weight: 400 !important;
}

/* Input styling enhancements */
#mgr-ticket-container input::placeholder,
#mgr-ticket-container textarea::placeholder {
  color: #7f8c8d !important;
  font-size: 1.563rem !important;
  font-weight: 400 !important;
  opacity: 0.7 !important;
}

#mgr-ticket-container input:focus,
#mgr-ticket-container textarea:focus,
#mgr-ticket-container select:focus {
  outline: none !important;
  border-color: #e4342a !important;
  box-shadow: 0 0 0 2px rgba(228, 52, 42, 0.3) !important;
}

/* Button styling - consolidated */
#mgrBtnTicket,
#mgr-ticket-container button,
#mgr-ticket-container .btn,
#mgr-ticket-container .btn-primary {
  background-color: #e4342a !important;
  color: #f8f8ff !important;
  font-size: 1.563rem !important;
  font-weight: 600 !important;
  line-height: 1.3em !important;
  padding: 10px 40px !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Inter', Arial, sans-serif !important;
}

#mgrBtnTicket:hover,
#mgr-ticket-container button:hover,
#mgr-ticket-container .btn:hover,
#mgr-ticket-container .btn-primary:hover {
  background-color: #454545 !important;
}

#mgrBtnTicket span,
#mgr-ticket-container button span,
#mgr-ticket-container .btn span,
#mgr-ticket-container .btn-primary span {
  color: #f8f8ff !important;
  background-color: transparent !important;
}

/* Alert/Message boxes - base styling */
#mgr-ticket-container .alert,
#mgr-ticket-container .message-container,
#mgr-ticket-container .message-container > div {
  border-radius: 4px !important;
  padding: 15px 20px !important;
  margin-bottom: 20px !important;
  font-family: 'Inter', Arial, sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #f8f8ff !important;
}

/* Message type variations */
#mgr-ticket-container .alert-danger,
#mgr-ticket-container .message-container .alert-danger,
#mgr-ticket-container .alert-error {
  background-color: #e4342a !important;
  border: 2px solid #c92a22 !important;
}

#mgr-ticket-container .alert-info,
#mgr-ticket-container #phoneNumberMsg {
  background-color: rgba(52, 152, 219, 0.3) !important;
  border: 1px solid #3498db !important;
}

#mgr-ticket-container .alert-success {
  background-color: rgba(46, 204, 113, 0.3) !important;
  border: 1px solid #2ecc71 !important;
}

#mgr-ticket-container .alert-warning {
  background-color: rgba(241, 196, 15, 0.3) !important;
  border: 1px solid #f1c40f !important;
}

/* Form groups and miscellaneous */
#mgr-ticket-container .form-group {
  margin-bottom: 15px !important;
}

/* Links */
#mgr-ticket-container a {
  text-decoration: none;
  color: #95a5a6;
  transition: color 0.3s;
  font-size: 1.563rem;
}

#mgr-ticket-container a:hover {
  color: #e4342a !important;
}

/* Hide "Powered By" section */
#mgr-ticket-container a[href="https://www.mygadgetrepairs.com"],
#mgr-ticket-container a[href="https://www.mygadgetrepairs.com"] span,
#mgr-ticket-container a[href="https://www.mygadgetrepairs.com"] img {
  display: none !important;
}

/* Widget margin reset */
#mgr-ticket-widget,
#mgrTicket {
  margin-bottom: 0 !important;
}