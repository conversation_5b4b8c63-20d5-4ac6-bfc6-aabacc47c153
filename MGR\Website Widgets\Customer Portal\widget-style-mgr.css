/* Font Face Definitions */
@font-face {
    font-family: 'Inter';
    src: url('https://sekigadgets.com.au/wp-content/uploads/inter-regular-webfont.woff2') format('woff2');       
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Inter';
    src: url('https://sekigadgets.com.au/wp-content/uploads/inter-semi-bold-webfont.woff2') format('woff2');       
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Inter';
    src: url('https://sekigadgets.com.au/wp-content/uploads/inter-bold-webfont.woff2') format('woff2');       
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Style for the heading section */
#page-title {
    background-color: #232729 !important;
    color: #F8F8FF !important;
    padding: 20px 0 !important;
}

#page-title .container {
    width: 100% !important;
    max-width: none !important;
}

#page-title .container > div {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 120px !important;
}

#page-title h1 {
    font-family: 'Inter', sans-serif !important;
    color: #F8F8FF !important;
    text-align: center !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 4.768rem !important;
    text-transform: uppercase !important;
    font-weight: 700 !important;
}

/* Remove logo */
.logo img, 
#header .logo img, 
#header-wrap .logo img,
#page-title img {
    display: none !important;
}

/* Form styling */
#tab-login-register {
    max-width: 500px !important;
    margin: 0 auto !important;
    padding: 20px !important;
}

/* Button styling */
.button.button-3d.button-black,
.button,
button[type="submit"] {
    background-color: #E4342A !important;
    color: #F8F8FF !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.563rem !important;
    line-height: 1.2em !important;
    padding: 10px 25px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: background-color 0.3s ease !important;
    text-transform: capitalize !important;
}

.button:hover,
button[type="submit"]:hover {
    background-color: #c62a21 !important;
}

/* Reset styles for links */
a {
    color: #E4342A !important;
    text-decoration: none !important;
    font-size: 1.563rem !important;
    font-family: 'Inter', sans-serif !important;
}

a:hover {
    text-decoration: underline !important;
}

/* Hide language selectors */
select#language,
select[name="language"],
div:has(> select#language),
div:has(> select[name="language"]),
.form-group:has(> select#language),
.form-group:has(> select[name="language"]),
[id*="language-selector"],
[class*="language-selector"],
[id*="lang-select"],
[class*="lang-select"],
*[id*="language"],
*[class*="language"],
*[id*="lang"],
*[class*="lang"],
select:has(option[value="en"]):has(option[value="fr"]),
select:has(option[value="en"]):has(option[value="es"]) {
    display: none !important;
}

/* Hide the alert box */
.alert.alert-info,
div[class*="alert-info"],
div[class*="alert"][class*="info"] {
    display: none !important;
}

/* Hide "Create Ticket" and "Preset Wizard - Kiosk" buttons */
.main-buttons a[href*="create-ticket"],
.main-buttons a[href*="preset-wizard"] {
    display: none !important;
}

/* Hide the parent columns of the hidden buttons */
.main-buttons .col-xs-12:has(> a[href*="create-ticket"]),
.main-buttons .col-xs-12:has(> a[href*="preset-wizard"]) {
    display: none !important;
}

/* Small text styling */
#tab-login-register small {
    font-family: 'Inter', sans-serif !important;
    font-size: 1rem !important;
    color: #6c757d !important;
    display: block !important;
    margin-bottom: 15px !important;
}

/* Responsive adjustments */
@media screen and (max-width: 991px) {
    .col-lg-3,
    .col-lg-6 {
        width: 100%;
    }
}

@media screen and (max-width: 768px) {
    #page-title h1 {
        font-size: 3.052rem !important;
    }

    #tab-login-register {
        padding: 15px !important;
    }

    .button.button-3d.button-black,
    .button,
    button[type="submit"] {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
}