<style type="text/css">/* Base template styles */ #template { font-size: 15px; margin: 10px auto; max-width: 1200px; padding: 0 15px; } /* Header styles */ .header-container { width: 100%; margin-bottom: 20px; display: table; table-layout: fixed; } .logo-section { display: table-cell; width: 50%; vertical-align: top; padding-right: 15px; } .logo-section img { max-width: 300px; max-height: 100px; height: auto; display: block; } .company-details { display: table-cell; width: 50%; vertical-align: top; text-align: right; } .shop-title { font-size: 18px; font-weight: bold; margin: 12px 0; } .contact-info { line-height: 1.4; margin: 12px 0 0 0; } .contact-info p { margin: 0; padding: 0; } /* Table styles */ .table-full { width: 100%; border-collapse: collapse; margin: 0; padding: 0; } .table-spacing { margin-bottom: 20px; } /* Document title styles */ .document-title { text-align: center; padding: 15px 0; margin-bottom: 20px; } .document-title h1 { font-size: 36px; font-weight: bold; margin: 0; text-transform: none; } /* Section styles */ .section-title { text-align: left; background-color: #f5f5f5; border: 1px solid #ddd; vertical-align: middle; height: 30px; line-height: 30px; padding: 0 10px; margin: 0; text-transform: uppercase; font-weight: bold; } .section-content { padding: 15px 10px; line-height: 1.4; } /* Line items table styles */ .line-items { border: 1px solid #999; width: 100%; margin-bottom: 20px; } .line-items thead th, .line-items tbody td { padding: 10px; border: 1px solid #999; } .line-items thead th { background-color: #f5f5f5; font-weight: bold; } /* Center specific column headers */ .line-items thead th:nth-child(3), .line-items thead th:nth-child(4), .line-items thead th:nth-child(5), .line-items thead th:nth-child(6), .line-items thead th:nth-child(7) { text-align: center; } /* Right align cell values */ .line-items td:nth-child(4), .line-items td:nth-child(5), .line-items td:nth-child(6), .line-items td:nth-child(7) { text-align: right; } /* Remove padding from line items container */ .table-full .section-content .line-items { margin: 0; } .table-full td .line-items { margin: -1px; } /* Summary section styles */ .summary-table { width: 100%; margin-top: 20px; } .summary-cell { text-align: right; padding: 8px; font-weight: normal; } .summary-amount { text-align: right; width: 120px; padding: 8px; font-weight: normal; } .total-row .summary-amount { font-weight: bold; } /* Print-specific styles */ @media print { #template { margin: 0; padding: 0; max-width: none; } .header-container { position: relative; page-break-inside: avoid; } .logo-section img { max-height: 100px; } .company-details { min-height: 100px; } .print-page-break { page-break-before: always; break-before: page; height: 0; display: block; } .section-title { page-break-inside: avoid; break-inside: avoid; } table { page-break-inside: auto; } tr { page-break-inside: avoid; } }
</style>
<div id="template">
<div class="header-container">
<div class="logo-section"><img alt="Seki Gadgets Logo" src="https://imagedelivery.net/cr6-k0T-tqcPAkf3VITtbg/7683b48f-b06b-45a5-f1a9-4dc09ace8800/public" /></div>

<div class="company-details">
<div class="shop-title">SEKI GADGETS</div>

<div class="contact-info">
<p>a: Unit 83/51 Nullarbor Avenue<br />
Franklin, ACT 2913, AUSTRALIA<br />
p: +***********<br />
e: <EMAIL><br />
w: https://sekigadgets.com.au/<br />
ABN: ***********</p>
</div>
</div>
</div>

<div class="document-title">
<h1>SALES INVOICE</h1>
</div>

<table class="table-full table-spacing">
	<tbody>
		<tr>
			<td style="width: 50%; vertical-align: top; padding-right: 15px;">
			<table class="table-full">
				<thead>
					<tr>
						<th class="section-title">BILL TO CUSTOMER</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="section-content"><strong>Name:</strong> [CUSTOMER_NAME][CUSTOMER_COMPANY_NAME]<br />
						<strong>Mobile:</strong> [CUSTOMER_MOBILE]<br />
						<strong>Email:</strong> [CUSTOMER_EMAIL]</td>
					</tr>
				</tbody>
			</table>
			</td>
			<td style="width: 50%; vertical-align: top;">
			<table class="table-full">
				<thead>
					<tr>
						<th class="section-title">INVOICE DETAIL</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="section-content"><strong>Invoice No:</strong> [INVOICE_REF]<br />
						<strong>Linked Ticket:</strong> [TICKET_REF]: [TICKET_SHORT_INFO]<br />
						<strong>Repair Status:</strong> [TICKET_CURRENT_STATUS]<br />
						<strong>Invoice Status:</strong> [INVOICE_PAID]<br />
						<strong>Invoice Date:</strong> [INVOICE_DATE]<br />
						<strong>Created By:</strong> [INVOICE_USER_NAME]<br />
						<strong>Due Date:</strong> [INVOICE_DUE_DATE]<br />
						<strong>Invoice Total:</strong> [GRAND_TOTAL]<br />
						[INVOICE_PAYMENTS]</td>
					</tr>
				</tbody>
			</table>
			</td>
		</tr>
	</tbody>
</table>

<table class="table-full table-spacing">
	<thead>
		<tr>
			<th class="section-title">LINE ITEMS</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td class="section-content">[LINE_ITEMS COLUMNS=&quot;#,Item,Qty,Price,Discount,GST, Total&quot; COLUMN_REFS=&quot;index,{product_code} - {product_name} {warranty_end_dt},quantity,${price},${subdiscount},$0.00,${subtotal}&quot; WIDTHS=&quot;5%,50%,5%,10%,10%,10%,10%&quot; CLASS=&quot;line-items&quot; THEAD_CLASS=&quot;bordered&quot; TBODY_CLASS=&quot;bordered&quot; TD_CLASS=&quot;bordered&quot; warranty_end_dt_label=&quot; - Warranty Ends: &quot; warranty_end_dt_type=&quot;date&quot;]</td>
		</tr>
	</tbody>
</table>

<table class="summary-table">
	<tbody>
		<tr>
			<td class="summary-cell">Subtotal:</td>
			<td class="summary-amount">[NET_TOTAL_WITH_DISCOUNT]</td>
		</tr>
		<tr>
			<td class="summary-cell">Discount:</td>
			<td class="summary-amount">[TOTAL_DISCOUNT]</td>
		</tr>
		<tr>
			<td class="summary-cell">GST:</td>
			<td class="summary-amount">[TOTAL_VAT]</td>
		</tr>
		<tr>
			<td class="summary-cell">Delivery Method (Customer Collect):</td>
			<td class="summary-amount">[TOTAL_DELIVERY_COST]</td>
		</tr>
		<tr class="total-row">
			<td class="summary-cell"><strong>Invoice Total:</strong></td>
			<td class="summary-amount">[GRAND_TOTAL]</td>
		</tr>
		<tr>
			<td class="summary-cell">Previous Payments:</td>
			<td class="summary-amount">[TOTAL_PAID]</td>
		</tr>
		<tr class="total-row">
			<td class="summary-cell"><strong>Balance Due:</strong></td>
			<td class="summary-amount">[BALANCE_DUE]</td>
		</tr>
	</tbody>
</table>

<table class="table-full table-spacing">
	<thead>
		<tr>
			<th class="section-title">ADDITIONAL NOTES</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td class="section-content">[DISCLAIMER]</td>
		</tr>
	</tbody>
</table>

<div class="print-page-break"></div>

<table class="table-full table-spacing">
	<thead>
		<tr>
			<th class="section-title">REPAIR DETAILS</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td class="section-content">[REPAIR_DETAIL]</td>
		</tr>
	</tbody>
</table>

<table class="table-full table-spacing">
	<thead>
		<tr>
			<th class="section-title">TICKET COMMENTS</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td class="section-content">[REPAIR_COMMENTS]</td>
		</tr>
	</tbody>
</table>

<table class="table-full table-spacing">
	<thead>
		<tr>
			<th class="section-title">PRE-POST REPAIR CONDITIONS</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td class="section-content">[REPAIR_CONDITIONS]</td>
		</tr>
	</tbody>
</table>
</div>