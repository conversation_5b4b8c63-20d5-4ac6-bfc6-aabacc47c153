/* Font Face Definitions */
@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-regular-webfont.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-semibold-webfont.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-bold-webfont.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

/* Main Container */
#mgr-widget-container {
  padding: 20px 40px 40px 40px !important;
  border-radius: 8px !important;
  font-family: 'Inter', sans-serif !important;
  /* DO NOT override background-color - let MGR's #232729 show */
}

/* Typography - consolidated */
#mgr-widget-container h3 {
  font-family: 'Inter', sans-serif !important;
  font-size: 2.441rem !important;
  font-weight: 700 !important;
  color: #f8f8ff !important;
  padding-bottom: 10px !important;
  background-color: transparent !important;
}

#mgr-widget-container h5,
#mgr-widget-container h6 {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #f8f8ff !important;
  background-color: transparent !important;
}

#mgr-widget-container h5 {
  padding-bottom: 10px !important;
}

/* Small tags - consolidated styling */
#mgr-widget-container small,
#mgr-widget-container > div > small,
#mgr-widget-container .form-group small {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 400 !important;
  color: #7f8c8d !important;
  background-color: transparent !important;
  display: inline !important;
}

#mgr-widget-container .form-group small {
  margin-left: 5px !important;
}

/* Form groups and labels */
#mgr-widget-container .form-group {
  margin-bottom: 15px !important;
}

/* Phone number form group alignment */
#mgr-widget-container .form-group label[for="mgr_phone_no"] {
  display: inline-block !important;
  margin-right: 5px !important;
  margin-bottom: 10px !important;
}

#mgr-widget-container .form-group label[for="mgr_phone_no"] + small {
  display: inline-block !important;
  margin-bottom: 10px !important;
  vertical-align: baseline !important;
}

/* Labels - consolidated with maximum specificity */
#mgr-widget-container label,
#mgr-widget-container .form-group label,
#mgr-widget-step-1 label,
div#mgr-widget-container label,
#mgr-widget-container div.form-group label,
label[for="mgr_full_name"],
label[for="mgr_phone_no"],
label[for="mgr_email"],
label[for="mgr_appointment_subject"],
label[for="mgr_appointment_location"] {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  width: auto !important;
  position: relative !important;
  clip: auto !important;
  overflow: visible !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #f8f8ff !important;
  background-color: transparent !important;
  margin-bottom: 10px !important;
  padding-bottom: 0 !important;
  line-height: 1.3 !important;
}

/* Specific labels with block display */
label[for="mgr_full_name"],
label[for="mgr_phone_no"],
label[for="mgr_email"],
label[for="mgr_appointment_subject"],
label[for="mgr_appointment_location"],
#mgr-widget-step-1 label {
  display: block !important;
  padding-bottom: 10px !important;
}

/* Week Navigator - dark theme styling */
#weekNavigator {
  margin: 20px 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  background-color: rgba(101, 101, 101, 0.5) !important;
  border: 1px solid #454545 !important;
}

#weekNavigator .row,
#weekNavigator table {
  padding: 10px !important;
  background-color: transparent !important;
  width: 100% !important;
}

/* Navigation controls */
#navLeft,
#navRight,
#navYear {
  font-size: 18px !important;
  color: #f8f8ff !important;
  background-color: transparent !important;
}

#navLeft,
#navRight {
  padding: 5px 10px !important;
  cursor: pointer !important;
}

#navLeft:hover,
#navRight:hover {
  color: #e4342a !important;
}

#navYear {
  font-weight: 700 !important;
}

/* Week row styling */
#weekRow {
  border-bottom: 5px solid rgba(101, 101, 101, 0.5) !important;
}

#weekRow td {
  padding: 10px !important;
  text-align: center !important;
  vertical-align: middle !important;
  cursor: pointer !important;
  color: #f8f8ff !important;
  background-color: transparent !important;
  border: 1px solid rgba(101, 101, 101, 0.3) !important;
  font-size: 20px !important;
  font-weight: normal !important;
}

#weekRow td:hover {
  background-color: rgba(228, 52, 42, 0.2) !important;
}

#weekRow td.chosen,
#weekRow td.selected {
  background-color: rgba(101, 101, 101, 0.5) !important;
}

#weekRow td:not(.chosen) {
  text-shadow: 1px 1px 1px #121212 !important;
}

#weekRow td[disabled],
#weekRow td[style*="line-through"] {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

/* Time Slots - dark theme styling */
#timeSlots {
  padding: 15px !important;
  border-radius: 5px !important;
  background-color: transparent !important;
  color: #f8f8ff !important;
}

#timeSlots h5,
#timeSlots h6 {
  color: #f8f8ff !important;
}

#timeSlots small {
  color: #7f8c8d !important;
}

#timeSlots .col-sm-4 > div,
#mgr-widget-container [data-timeslot] {
  padding: 10px 0 !important;
  margin: 5px !important;
  cursor: pointer !important;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s !important;
  background-color: transparent !important;
  color: #f8f8ff !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

#timeSlots .col-sm-4 > div:hover {
  background-color: rgba(228, 52, 42, 0.2) !important;
  border-color: #e4342a !important;
}

#timeSlots .col-sm-4 > div.selected,
#timeSlots .col-sm-4 > div.btn-primary,
#mgr-widget-container [data-timeslot].btn-primary {
  background-color: #e4342a !important;
  color: #f8f8ff !important;
  border-color: #e4342a !important;
}

/* Time slot elements */
#mgr-widget-container [data-timeslot] time,
#mgr-widget-container [data-timeslot] time span {
  color: inherit !important;
  background-color: transparent !important;
}

/* Time slot checkmark for selected */
[data-timeslot].btn-primary:after {
  content: "\2714" !important;
  position: absolute !important;
  right: 25px !important;
  color: #f8f8ff !important;
}

/* Form Fields - light fields on dark background */
#mgr-widget-container input[type="text"],
#mgr-widget-container input[type="email"],
#mgr-widget-container input[type="tel"],
#mgr-widget-container textarea,
#mgr-widget-container select,
#mgr-widget-container .form-control {
  width: 100% !important;
  padding: 15px !important;
  margin-bottom: 15px !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  color: #232729 !important;
  background-color: #f8f8ff !important;
  line-height: 1.5 !important;
  font-weight: 400 !important;
}

#mgr-widget-container input::placeholder,
#mgr-widget-container textarea::placeholder {
  color: #7f8c8d !important;
  font-size: 1.563rem !important;
  font-weight: 400 !important;
  opacity: 0.7 !important;
}

#mgr-widget-container input:focus,
#mgr-widget-container textarea:focus,
#mgr-widget-container select:focus {
  outline: none !important;
  border-color: #e4342a !important;
  box-shadow: 0 0 0 2px rgba(228, 52, 42, 0.3) !important;
}

/* Select2 and phone input styling */
#mgr-widget-container .select2-container {
  width: 100% !important;
}

#mgr-widget-container .select2-container span,
#mgr-widget-container .select2-selection {
  background-color: #f8f8ff !important;
  color: #232729 !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
}

/* International phone input styling */
#mgr-widget-container .mgr-prefix-wrapper {
  position: relative !important;
}

#mgr-widget-container .iti {
  width: 100% !important;
  display: block !important;
}

#mgr-widget-container .iti-flag {
  background-color: transparent !important;
}

#mgr-widget-container .selected-flag {
  padding: 0 !important;
  background-color: #f8f8ff !important;
  border-right: 1px solid #656565 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
}

#mgr-widget-container .iti-arrow {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-top: 5px solid #232729 !important;
  margin-left: 5px !important;
}

#mgr-widget-container .selected-dial-code {
  display: none !important;
}

/* Phone input field adjustments */
#mgr-widget-container input[type="tel"],
#mgr-widget-container input#mgr_phone_no {
  padding-left: 70px !important;
  font-size: 1.563rem !important;
  width: 100% !important;
}

/* Country dropdown styling */
#mgr-widget-container .country-list {
  background-color: #f8f8ff !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
}

#mgr-widget-container .country-list .country {
  padding: 10px 15px !important;
  color: #232729 !important;
  font-size: 1rem !important;
  font-family: 'Inter', sans-serif !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
}

#mgr-widget-container .country-list .country .country-name {
  color: #232729 !important;
  background-color: transparent !important;
  font-size: 1rem !important;
  margin-left: 8px !important;
  flex-grow: 1 !important;
}

#mgr-widget-container .country-list .country .dial-code {
  color: #7f8c8d !important;
  background-color: transparent !important;
  font-size: 0.875rem !important;
  margin-left: auto !important;
}

#mgr-widget-container .country-list .country .flag-box {
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
}

#mgr-widget-container .country-list .country:hover {
  background-color: #ecf8f3 !important;
}

#mgr-widget-container .country-list .country.active,
#mgr-widget-container .country-list .country.highlight {
  background-color: #e4342a !important;
}

#mgr-widget-container .country-list .country.active .country-name,
#mgr-widget-container .country-list .country.active .dial-code,
#mgr-widget-container .country-list .country.highlight .country-name,
#mgr-widget-container .country-list .country.highlight .dial-code {
  color: #f8f8ff !important;
}

/* Country search box */
#mgr-widget-container .country-list .search-box {
  padding: 10px !important;
  border-bottom: 1px solid #656565 !important;
}

#mgr-widget-container .country-list .search-box input {
  width: 100% !important;
  padding: 8px !important;
  font-size: 1rem !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
}

/* Button styling - consolidated */
#mgr-widget-container button,
#mgr-widget-container .btn,
#mgr-widget-container .btn-primary {
  background-color: #e4342a !important;
  color: #f8f8ff !important;
  font-size: 1.563rem !important;
  font-weight: 600 !important;
  line-height: 1.3em !important;
  padding: 10px 40px !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Inter', sans-serif !important;
  margin-top: 25px !important;
}

#mgr-widget-container button:hover,
#mgr-widget-container .btn:hover,
#mgr-widget-container .btn-primary:hover {
  background-color: #454545 !important;
}

#mgr-widget-container button span,
#mgr-widget-container .btn span,
#mgr-widget-container .btn-primary span {
  color: #f8f8ff !important;
  background-color: transparent !important;
}

/* Start Over button - inverted colors */
#mgr-widget-container #mgrBtnStartOver,
#mgr-widget-container .btn-warning {
  background-color: #454545 !important;
  color: #f8f8ff !important;
}

#mgr-widget-container #mgrBtnStartOver:hover,
#mgr-widget-container .btn-warning:hover {
  background-color: #e4342a !important;
}

/* Alert/Message boxes - base styling */
#mgr-widget-container .alert,
#mgr-widget-container .message-container,
#mgr-widget-container .message-container > div {
  border-radius: 4px !important;
  padding: 15px 20px !important;
  margin-bottom: 20px !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  color: #f8f8ff !important;
}

/* Message type variations */
#mgr-widget-container .alert-danger,
#mgr-widget-container .message-container .alert-danger,
#mgr-widget-container .alert-error {
  background-color: #e4342a !important;
  border: 2px solid #c92a22 !important;
}

#mgr-widget-container .alert-info,
#mgr-widget-container #phoneNumberMsg {
  background-color: rgba(52, 152, 219, 0.3) !important;
  border: 1px solid #3498db !important;
}

#mgr-widget-container .alert-success {
  background-color: rgba(46, 204, 113, 0.3) !important;
  border: 1px solid #2ecc71 !important;
}

#mgr-widget-container .alert-warning {
  background-color: rgba(241, 196, 15, 0.3) !important;
  border: 1px solid #f1c40f !important;
}

/* Loading layer */
#mgr-widget-container .loader-layer {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  #mgr-widget-container {
    padding: 30px 20px !important;
  }

  .form-full {
    display: none !important;
  }

  .form-three {
    display: block !important;
  }

  .form-two {
    display: none !important;
  }

  #mgr-widget-container h3 {
    font-size: 2rem !important;
  }

  #mgr-widget-container label,
  #mgr-widget-container h5 {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 480px) {
  .form-full,
  .form-three {
    display: none !important;
  }

  .form-two {
    display: block !important;
  }

  #mgr-widget-container {
    padding: 30px 15px !important;
  }

  #mgr-widget-container h3 {
    font-size: 1.75rem !important;
  }

  #mgr-widget-container button {
    font-size: 1.25rem !important;
    padding: 8px 30px !important;
  }
}

/* Miscellaneous overrides */
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"],
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"] span,
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"] img {
  display: none !important;
}

#mgr-widget-container label[style*="display: none"] {
  display: block !important;
}

#mgr-widget-container label[style*="visibility: hidden"] {
  visibility: visible !important;
}

#mgr-widget-container p,
#mgr-widget-container div,
#mgr-widget-container span:not(.iti-flag):not(.select2-selection__arrow) {
  color: inherit !important;
}

#mgr-widget-container .clearfix {
  clear: both !important;
}