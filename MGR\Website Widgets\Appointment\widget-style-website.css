/* Font Face Definitions */
@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-regular-webfont.woff2') format('woff2');       
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-semibold-webfont.woff2') format('woff2');       
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'Inter';
  src: url('https://sekigadgets.com.au/wp-content/uploads/inter-bold-webfont.woff2') format('woff2');       
  font-weight: 700;
  font-style: normal;
}

/* Main Container - Let MGR's dark background show through */
#mgr-widget-container {
  padding: 20px 40px 40px 40px !important;
  border-radius: 8px !important;
  font-family: 'Inter', sans-serif !important;
  /* DO NOT override background-color - let MGR's #232729 show */
}

/* Styling for H3 Headings in Widget Container */
#mgr-widget-container h3 {
  font-family: 'Inter', sans-serif !important;
  font-size: 2.441rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  padding-bottom: 10px !important;
  background-color: transparent !important;
}

/* Styling for Small Tags */
#mgr-widget-container small {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 400 !important;
  color: #7f8c8d !important;
  background-color: transparent !important;
  display: inline !important;
}

/* Styling for Small Tags in Main Container */
#mgr-widget-container > div > small {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 400 !important;
  color: #7f8c8d !important;
  background-color: transparent !important;
  display: inline !important;
}

/* Styling for Small Tags in Form Groups */
#mgr-widget-container .form-group small {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 400 !important;
  color: #7f8c8d !important;
  background-color: transparent !important;
  display: inline !important;
  margin-left: 5px !important;
}

/* Styling for H5 Headings in Widget Container */
#mgr-widget-container h5 {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  padding-bottom: 10px !important;
  background-color: transparent !important;
}

/* Form groups should allow inline label + small text */
#mgr-widget-container .form-group {
  margin-bottom: 15px !important;
}

/* Specific styling for phone number form group to align label and instruction */
#mgr-widget-container .form-group label[for="mgr_phone_no"] {
  display: inline-block !important;
  margin-right: 5px !important;
  margin-bottom: 10px !important;
}

#mgr-widget-container .form-group label[for="mgr_phone_no"] + small {
  display: inline-block !important;
  margin-bottom: 10px !important;
  vertical-align: baseline !important;
}

/* Force ALL labels to display with maximum specificity */
#mgr-widget-container label,
#mgr-widget-container .form-group label,
#mgr-widget-step-1 label,
div#mgr-widget-container label,
#mgr-widget-container div.form-group label {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  width: auto !important;
  position: relative !important;
  clip: auto !important;
  overflow: visible !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
  margin-bottom: 10px !important;
  padding-bottom: 0 !important;
  line-height: 1.3 !important;
}

/* Specific label targeting by ID for maximum override power */
label[for="mgr_full_name"],
label[for="mgr_phone_no"],
label[for="mgr_email"],
label[for="mgr_appointment_subject"],
label[for="mgr_appointment_location"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
  margin-bottom: 10px !important;
  padding-bottom: 10px !important;
}

/* Additional override for Step 1 labels specifically */
#mgr-widget-step-1 label {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
  padding-bottom: 10px !important;
}

/* Styling for H6 Headings */
#mgr-widget-container h6 {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
}

/* Week Navigator - Styled for dark theme */
#weekNavigator {
  margin: 20px 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  background-color: rgba(101, 101, 101, 0.5) !important;
  border: 1px solid #454545 !important;
}

#weekNavigator .row {
  padding: 10px !important;
  background-color: transparent !important;
}

#weekNavigator table {
  width: 100% !important;
  background-color: transparent !important;
}

#navLeft, #navRight {
  font-size: 18px !important;
  padding: 5px 10px !important;
  cursor: pointer !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
}

#navLeft:hover, #navRight:hover {
  color: #E4342A !important;
}

#navYear {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
}

#weekRow {
  border-bottom: 5px solid rgba(101, 101, 101, 0.5) !important;
}

#weekRow td {
  padding: 10px !important;
  text-align: center !important;
  vertical-align: middle !important;
  cursor: pointer !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
  border: 1px solid rgba(101, 101, 101, 0.3) !important;
}

#weekRow td:hover {
  background-color: rgba(228, 52, 42, 0.2) !important;
}

#weekRow td.chosen,
#weekRow td.selected {
  background-color: rgba(101, 101, 101, 0.5) !important;
  color: #F8F8FF !important;
}

/* Disabled/past dates */
#weekRow td[disabled],
#weekRow td[style*="line-through"] {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

/* Time Slots - Dark theme styling */
#timeSlots {
  padding: 15px !important;
  border-radius: 5px !important;
  background-color: transparent !important;
  color: #F8F8FF !important;
}

#timeSlots h5,
#timeSlots h6 {
  color: #F8F8FF !important;
}

#timeSlots small {
  color: #7f8c8d !important;
}

#timeSlots .col-sm-4 > div {
  padding: 10px 0 !important;
  margin: 5px !important;
  cursor: pointer !important;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s !important;
  background-color: transparent !important;
  color: #F8F8FF !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  text-align: center !important;
}

#timeSlots .col-sm-4 > div:hover {
  background-color: rgba(228, 52, 42, 0.2) !important;
  border-color: #E4342A !important;
}

#timeSlots .col-sm-4 > div.selected,
#timeSlots .col-sm-4 > div.btn-primary,
#mgr-widget-container [data-timeslot].btn-primary {
  background-color: #E4342A !important;
  color: #F8F8FF !important;
  border-color: #E4342A !important;
}

/* Time slot checkmark for selected */
[data-timeslot].btn-primary:after {
  content: "\2714" !important;
  position: absolute !important;
  right: 25px !important;
  color: #F8F8FF !important;
}

/* Form Fields - Light fields on dark background */
#mgr-widget-container input[type="text"],
#mgr-widget-container input[type="email"],
#mgr-widget-container input[type="tel"],
#mgr-widget-container textarea,
#mgr-widget-container select,
#mgr-widget-container .form-control {
  width: 100% !important;
  padding: 15px !important;
  margin-bottom: 15px !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  color: #232729 !important;
  background-color: #F8F8FF !important;
  line-height: 1.5 !important;
  font-weight: 400 !important;
}

#mgr-widget-container input::placeholder,
#mgr-widget-container textarea::placeholder {
  color: #7f8c8d !important;
  font-size: 1.563rem !important;
  font-weight: 400 !important;
  opacity: 0.7 !important;
}

#mgr-widget-container input:focus,
#mgr-widget-container textarea:focus,
#mgr-widget-container select:focus {
  outline: none !important;
  border-color: #E4342A !important;
  box-shadow: 0 0 0 2px rgba(228, 52, 42, 0.3) !important;
}

/* Select2 dropdown styling */
#mgr-widget-container .select2-container {
  width: 100% !important;
}

#mgr-widget-container .select2-container span {
  background-color: #F8F8FF !important;
  color: #232729 !important;
}

#mgr-widget-container .select2-selection {
  background-color: #F8F8FF !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
}

/* Country selector for phone input */
#mgr-widget-container .iti-flag {
  background-color: transparent !important;
}

/* Fix for intl-tel-input country selector */
#mgr-widget-container .mgr-prefix-wrapper {
  position: relative !important;
}

/* Main country selector container */
#mgr-widget-container .iti {
  width: 100% !important;
  display: block !important;
}

/* Style the country selector dropdown trigger */
#mgr-widget-container .selected-flag {
  padding: 0 !important;
  background-color: #F8F8FF !important;
  border-right: 1px solid #656565 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
}

/* Arrow in the country selector */
#mgr-widget-container .iti-arrow {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-top: 5px solid #232729 !important;
  margin-left: 5px !important;
}

/* Country code dropdown list container */
#mgr-widget-container .country-list {
  background-color: #F8F8FF !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
}

/* Individual country items in dropdown */
#mgr-widget-container .country-list .country {
  padding: 10px 15px !important;
  color: #232729 !important;
  font-size: 1rem !important;
  font-family: 'Inter', sans-serif !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
}

/* Country name text */
#mgr-widget-container .country-list .country .country-name {
  color: #232729 !important;
  background-color: transparent !important;
  font-size: 1rem !important;
  margin-left: 8px !important;
  flex-grow: 1 !important;
}

/* Dial code in dropdown */
#mgr-widget-container .country-list .country .dial-code {
  color: #7f8c8d !important;
  background-color: transparent !important;
  font-size: 0.875rem !important;
  margin-left: auto !important;
}

/* Hover state for country items */
#mgr-widget-container .country-list .country:hover {
  background-color: #ECF8F3 !important;
}

/* Active/selected country in dropdown */
#mgr-widget-container .country-list .country.active,
#mgr-widget-container .country-list .country.highlight {
  background-color: #E4342A !important;
}

#mgr-widget-container .country-list .country.active .country-name,
#mgr-widget-container .country-list .country.active .dial-code,
#mgr-widget-container .country-list .country.highlight .country-name,
#mgr-widget-container .country-list .country.highlight .dial-code {
  color: #F8F8FF !important;
}

/* Phone input field - adjust for country selector */
#mgr-widget-container input[type="tel"],
#mgr-widget-container input#mgr_phone_no {
  padding-left: 70px !important;
  font-size: 1.563rem !important;
  width: 100% !important;
}

/* Country code text in the selector button */
#mgr-widget-container .selected-dial-code {
  display: none !important;
}

/* Flag box in dropdown */
#mgr-widget-container .country-list .country .flag-box {
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
}

/* Search box in country dropdown (if present) */
#mgr-widget-container .country-list .search-box {
  padding: 10px !important;
  border-bottom: 1px solid #656565 !important;
}

#mgr-widget-container .country-list .search-box input {
  width: 100% !important;
  padding: 8px !important;
  font-size: 1rem !important;
  border: 1px solid #656565 !important;
  border-radius: 4px !important;
}

/* Global Button Styling */
#mgr-widget-container button,
#mgr-widget-container .btn {
  background-color: #E4342A !important;
  color: #F8F8FF !important;
  font-size: 1.563rem !important;
  font-weight: 600 !important;
  line-height: 1.3em !important;
  padding: 10px 40px !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Inter', sans-serif !important;
  margin-top: 25px !important;
}

#mgr-widget-container button:hover,
#mgr-widget-container .btn:hover {
  background-color: #454545 !important;
}

/* Button span text color override */
#mgr-widget-container button span,
#mgr-widget-container .btn span {
  color: #F8F8FF !important;
  background-color: transparent !important;
}

/* Primary button styling */
#mgr-widget-container .btn-primary {
  background-color: #E4342A !important;
  color: #F8F8FF !important;
}

#mgr-widget-container .btn-primary:hover {
  background-color: #454545 !important;
}

#mgr-widget-container .btn-primary span {
  color: #F8F8FF !important;
}

/* Styling For "Start Over" Button */
#mgr-widget-container #mgrBtnStartOver,
#mgr-widget-container .btn-warning {
  background-color: #454545 !important;
  color: #F8F8FF !important;
}

#mgr-widget-container #mgrBtnStartOver:hover,
#mgr-widget-container .btn-warning:hover {
  background-color: #E4342A !important;
}

/* Styling for Time Slots */
#mgr-widget-container [data-timeslot] {
  font-family: 'Inter', sans-serif !important;
  color: #F8F8FF !important;
}

#mgr-widget-container [data-timeslot] time {
  color: inherit !important;
}

#mgr-widget-container [data-timeslot] time span {
  color: inherit !important;
  background-color: transparent !important;
}

/* Styling for Appointment Summary Labels */
#mgr-widget-container label[for="mgr_appointment_subject"],
#mgr-widget-container label[for="mgr_appointment_location"] {
  font-family: 'Inter', sans-serif !important;
  font-size: 1.563rem !important;
  font-weight: 700 !important;
  color: #F8F8FF !important;
  background-color: transparent !important;
  padding-bottom: 10px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Alert/Message boxes */
#mgr-widget-container .alert,
#mgr-widget-container .message-container,
#mgr-widget-container .message-container > div {
  border-radius: 4px !important;
  padding: 15px 20px !important;
  margin-bottom: 20px !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
}

/* Error messages */
#mgr-widget-container .alert-danger,
#mgr-widget-container .message-container .alert-danger,
#mgr-widget-container .alert-error {
  background-color: #E4342A !important;
  border: 2px solid #C92A22 !important;
  color: #F8F8FF !important;
}

/* Info messages */
#mgr-widget-container .alert-info,
#mgr-widget-container #phoneNumberMsg {
  background-color: rgba(52, 152, 219, 0.3) !important;
  border: 1px solid #3498db !important;
  color: #F8F8FF !important;
}

/* Success messages */
#mgr-widget-container .alert-success {
  background-color: rgba(46, 204, 113, 0.3) !important;
  border: 1px solid #2ecc71 !important;
  color: #F8F8FF !important;
}

/* Warning messages */
#mgr-widget-container .alert-warning {
  background-color: rgba(241, 196, 15, 0.3) !important;
  border: 1px solid #f1c40f !important;
  color: #F8F8FF !important;
}

/* Loading layer */
#mgr-widget-container .loader-layer {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  #mgr-widget-container {
    padding: 30px 20px 30px 20px !important;
  }
  
  .form-full {
    display: none !important;
  }
  .form-three {
    display: block !important;
  }
  .form-two {
    display: none !important;
  }

  #mgr-widget-container h3 {
    font-size: 2rem !important;
  }

  #mgr-widget-container label,
  #mgr-widget-container h5 {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 480px) {
  .form-full {
    display: none !important;
  }
  .form-three {
    display: none !important;
  }
  .form-two {
    display: block !important;
  }

  #mgr-widget-container {
    padding: 30px 15px 30px 15px !important;
  }

  #mgr-widget-container h3 {
    font-size: 1.75rem !important;
  }

  #mgr-widget-container button {
    font-size: 1.25rem !important;
    padding: 8px 30px !important;
  }
}

/* Hide "Powered By" section */
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"],
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"] span,
#mgr-widget-container a[href="https://www.mygadgetrepairs.com"] img {
  display: none !important;
}

/* Additional overrides to ensure labels are never hidden */
#mgr-widget-container label[style*="display: none"] {
  display: block !important;
}

#mgr-widget-container label[style*="visibility: hidden"] {
  visibility: visible !important;
}

/* Ensure all text elements have proper colors for dark theme */
#mgr-widget-container p,
#mgr-widget-container div,
#mgr-widget-container span:not(.iti-flag):not(.select2-selection__arrow) {
  color: inherit !important;
}

/* Week row text styling */
#weekRow > table td {
  font-size: 20px !important;
  font-weight: normal !important;
}

#weekRow > table td:not(.chosen) {
  text-shadow: 1px 1px 1px #121212 !important;
}

/* Clearfix helper */
#mgr-widget-container .clearfix {
  clear: both !important;
}