/* Globals */

/* Base Font Size */
html{
	font-size: 62.5%;
}

/* Disable Notes */
#wp-admin-bar-elementor_notes {
display: none;
}

/* End Globals */

/* Header */

/* Logo Animation On Scroll */
.elementor-sticky--effects .c-logo img {
max-width: 130px;
height: auto;
transition: all 0.5s ease;
}

/* Mobile Menu */
.force-hide-mobile {
@media screen and (max-width: 767px) {
display: none!important;
}
}

.mobile-menu-font .elementor-nav-menu .elementor-item {
  font-size: 5rem;
  font-weight: 700;
  color: #e4342a;
}

/* End Header */

/* Custom Styles */

/* Outline Button Light */
.button-outline-light .elementor-button{
    background-color: #454545;
    color: white;
    border-radius: 3px;
    box-shadow: inset 0px 0px 0px 2px white;}

/* Outline Button Dark */
.button-outline-dark .elementor-button{
    background-color: transparent;
    color: #454545;
    border-radius: 3px;
    box-shadow: inset 0px 0px 0px 2px white;}

/* Dark Button*/
.button-dark .elementor-button{
    background-color: var(--e-global-color-accent );
    color: #FFFFFF;}

/* Read More */
.button-read-more-light .elementor-button {
  background-color: transparent;
  color: #454545;
  text-decoration: underline 1px solid #454545;
  position: relative;
	text-align: left;
	padding-right: 30px;
}

.button-read-more-light .elementor-button-icon {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-40%);
}

.button-read-more-light .elementor-button-icon svg {
  fill: #454545;
}

/* Info Blocks */

.info-block h2 {
  font-size: 3.052rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

.info-block h3 {
  font-size: 2.441rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

.info-block h4 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

/* Body Text Fixes With Spacing */
.info-block p {
	padding-bottom: 10px;
	margin-bottom: 10px;
}

/* Link Styles */
.info-block p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.info-block ul,
.info-block ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
	padding-left: 20px;
}
.info-block ul li,
.info-block ol li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 10px;
}
/* Add Space Between Nested Bullets */
.info-block ul ul,
.info-block ul ol,
.info-block ol ul,
.info-block ol ol {
	margin-top: 10px;
	margin-bottom: 10px;
}
/* Styling for Deeper Nested Bullets */
.info-block ul ul ul,
.info-block ul ul ol,
.info-block ul ol ul,
.info-block ul ol ol,
.info-block ol ul ul,
.info-block ol ul ol,
.info-block ol ol ul,
.info-block ol ol ol {
	margin-left: 20px;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
	.info-block p {
	font-size: 1.563rem;
}
}

/* Text Editor Widget */
.legal-content h1 {
	font-size: 3.815rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h2 {
  font-size: 3.052rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h3 {
  font-size: 2.441rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h4 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h5 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h6 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

/* Body Text Fixes With Spacing */
.legal-content p {
	padding-bottom: 10px;
	margin-bottom: 10px;
}

/* Link Styles */
.legal-content p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.legal-content ul,
.legal-content ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
	padding-left: 20px;
}
.legal-content ul li,
.legal-content ol li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 10px;
}
/* Add Space Between Nested Bullets */
.legal-content ul ul,
.legal-content ul ol,
.legal-content ol ul,
.legal-content ol ol {
	margin-top: 10px;
	margin-bottom: 10px;
}
/* Styling for deeper nested lists */
.legal-content ul ul ul,
.legal-content ul ul ol,
.legal-content ul ol ul,
.legal-content ul ol ol,
.legal-content ol ul ul,
.legal-content ol ul ol,
.legal-content ol ol ul,
.legal-content ol ol ol {
	margin-left: 20px;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
	.legal-content p {
	font-size: 1.563rem;
}
}

/* Single Post */

/* Header Fixes With Spacing */
.post-toc .elementor-toc__header {
	border: none;
	padding-bottom: 20px;
}

.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  padding-top: 40px;
  font-weight: 700;
  color: #454545;
}

.post-content h2 {
	font-size: 3.052rem;
}

.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  font-size: 1.563rem;
}

/* Body Text Fixes With Spacing */
.post-content p {
	padding-bottom: 10px;
	margin-bottom: 0px;
}

/* Post Link Styles */
.post-content p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.c-post-meta ul li:nth-child(2) {
  font-weight: 400 !important;
}

.post-content ul,
.post-content ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
}

.post-content ul li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 20px;
}

/* Add Space Between Nested Bullets */
.post-content ul ul {
  margin-top: 10px;
}

.post-content ul ul li {
  margin-bottom: 10px;
}

/* Image Styles With Spacing */
.post-content .wp-block-image img {
  margin: 20px 0;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
  .post-content p {
    font-size: 1.563rem;
  }
}

/*Google logo colors*/
.g-blue{ color:#4285F4; }
.o-red{ color:#DB4437; }
.o-yellow{ color:#F4B400; }
.l-green{ color:#0F9D58; }
.e-red { display:inline block;transform:rotate(-20deg); }

/* End Custom Styles */

/* Footer */

/* End Footer */
