/* Globals */

/* Base Font Size */
html{
	font-size: 62.5%;
}

/* Disable Notes */
#wp-admin-bar-elementor_notes {
display: none;
}

/* CSS Snippet - image #1 */
.insert-image-1 {
	display: inline-block;
	 background-image: url("add image url");
	 background-repeat: no-repeat;
	 background-position: center center;
	 background-size: contain;
	 width: 1em;
	 height: 1em;
	 line-height: 1;
	 vertical-align: middle;
	 overflow: hidden;
 }
 
 /* CSS Snippet - image #2 */
 .insert-image-2 {
	display: inline-block;
	 background-image: url("add image url");
	 background-repeat: no-repeat;
	 background-position: center center;
	 background-size: contain;
	 width: 1em;
	 height: 1em;
	 line-height: 1;
	 vertical-align: middle;
	 overflow: hidden;
 }
 
 /* CSS Snippet - image #3 */
 .insert-image-3 {
	display: inline-block;
	 background-image: url("add image url");
	 background-repeat: no-repeat;
	 background-position: center center;
	 background-size: contain;
	 width: 1em;
	 height: 1em;
	 line-height: 1;
	 vertical-align: middle;
	 overflow: hidden;
 }

/* Global Container Variables*/
:root {
  /* Global Padding Variables - Editable */
  /* Left and Right Padding All /Sections/Containers*/
  --fluid-side-padding-min: 2rem; /* 20px with 62.5% root font size */
  --fluid-side-padding-max: 8rem;   /* 80px with 62.5% root font size */

  /* Top and Bottom Padding All Containers*/
  --section-xxl-padding-min: 15rem; /* 150px with 62.5% root font size */
  --section-xxl-padding-max: 16rem; /* 160px with 62.5% root font size */ 

  --section-xl-padding-min: 11rem; /* 110px with 62.5% root font size */
  --section-xl-padding-max: 12rem; /* 120px with 62.5% root font size */

  --section-l-padding-min: 9rem; /* 90px with 62.5% root font size */
  --section-l-padding-max: 10rem; /* 100px with 62.5% root font size */

  --section-m-padding-min: 8rem; /* 80px with 62.5% root font size */
  --section-m-padding-max: 8rem; /* 80px with 62.5% root font size */

  --section-s-padding-min: 6rem; /* 60px with 62.5% root font size */
  --section-s-padding-max: 6rem; /* 60px with 62.5% root font size */

  --section-xs-padding-min: 4rem; /* 40px with 62.5% root font size */
  --section-xs-padding-max: 4rem; /* 40px with 62.5% root font size */

  --section-xxs-padding-min: 2.4rem; /* 24px with 62.5% root font size */
  --section-xxs-padding-max: 2.4rem; /* 24px with 62.5% root font size */
  
  --section-header-padding-min: 2rem; /* 20px with 62.5% root font size */
  --section-header-padding-max: 2rem; /* 20px with 62.5% root font size */

  /* Hero Sections Height Variable */
  --section-hero-height: 100vh; /* 100% the screen height */

  /* Offset Padding for Overlay Headers */
  --section-offset-header: 8rem; /* 80px with 62.5% root font size */

  /* Width For Narrow Sections*/
  --section-narrow: 100rem; /* 1000px with 62.5% root font size */
  --section-narrow-xs: 72rem; /* 720px with 62.5% root font size */
}

/* Section/Container Padding - Fluid Variants */
.section-xxl {
  padding-top: clamp(var(--section-xxl-padding-min), 1.74vw + 14.61rem, var(--section-xxl-padding-max));
  padding-bottom: clamp(var(--section-xxl-padding-min), 1.74vw + 14.61rem, var(--section-xxl-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-xl {
  padding-top: clamp(var(--section-xl-padding-min), 1.74vw + 10.61rem, var(--section-xl-padding-max));
  padding-bottom: clamp(var(--section-xl-padding-min), 1.74vw + 10.61rem, var(--section-xl-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-l {
  padding-top: clamp(var(--section-l-padding-min), 1.74vw + 8.61rem, var(--section-l-padding-max));
  padding-bottom: clamp(var(--section-l-padding-min), 1.74vw + 8.61rem, var(--section-l-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-m {
  padding-top: clamp(var(--section-m-padding-min), 0vw + 8rem, var(--section-m-padding-max));
  padding-bottom: clamp(var(--section-m-padding-min), 0vw + 8rem, var(--section-m-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-s {
  padding-top: clamp(var(--section-s-padding-min), 0vw + 6rem, var(--section-s-padding-max));
  padding-bottom: clamp(var(--section-s-padding-min), 0vw + 6rem, var(--section-s-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-xs {
  padding-top: clamp(var(--section-xs-padding-min), 0vw + 4rem, var(--section-xs-padding-max));
  padding-bottom: clamp(var(--section-xs-padding-min), 0vw + 4rem, var(--section-xs-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-xxs {
  padding-top: clamp(var(--section-xxs-padding-min), 0vw + 2.4rem, var(--section-xxs-padding-max));
  padding-bottom: clamp(var(--section-xxs-padding-min), 0vw + 2.4rem, var(--section-xxs-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

.section-header {
  padding-top: clamp(var(--section-header-padding-min), 0vw + 2rem, var(--section-header-padding-max));
  padding-bottom: clamp(var(--section-header-padding-min), 0vw + 2rem, var(--section-header-padding-max));
  padding-left: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
  padding-right: clamp(var(--fluid-side-padding-min), 10.44vw + -0.35rem, var(--fluid-side-padding-max))!important;
}

/* Hero Container/Sections Height */
.section-hero {
  min-height: var(--section-hero-height)!important;
}

.section-hero .e-con-inner {
  justify-content: center!important;
}

/* Full Width Sections - No Side Padding */
.section-full div {
  max-width: 100%!important;
}

/* Narrow Sections */
.section-narrow .e-con-inner {
  max-width: var(--section-narrow)!important;
}

.section-narrow-xs .e-con-inner {
  max-width: var(--section-narrow-xs)!important;
}

/* Offset Padding for Overlay Headers */
.section-offset {
  padding-top: calc(var(--section-offset-header) + var(--section-xxl-padding-min));
}

/* End Globals */

/* Header */

/* Logo Animation On Scroll */
.elementor-sticky--effects .c-logo img {
max-width: 130px;
height: auto;
transition: all 0.5s ease;
}

/* Mobile Display Classes */
.force-hide-mobile {
@media screen and (max-width: 767px) {
display: none!important;
}
}

/* End Header */

/* Custom Styles */

/* Outline Button Light */
.button-outline-light .elementor-button{
    background-color: #454545;
    color: white;
    border-radius: 3px;
    box-shadow: inset 0px 0px 0px 2px white;}

/* Outline Button Dark */
.button-outline-dark .elementor-button{
    background-color: transparent;
    color: #454545;
    border-radius: 3px;
    box-shadow: inset 0px 0px 0px 2px white;}

/* Dark Button*/
.button-dark .elementor-button{
    background-color: var(--e-global-color-accent );
    color: #FFFFFF;}

/* Read More */
.button-read-more-light .elementor-button {
  background-color: transparent;
  color: #454545;
  text-decoration: underline 1px solid #454545;
  position: relative;
	text-align: left;
	padding-right: 30px;
}

.button-read-more-light .elementor-button-icon {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-40%);
}

.button-read-more-light .elementor-button-icon svg {
  fill: #454545;
}

/* Info Blocks */

.info-block h2 {
  font-size: 3.052rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

.info-block h3 {
  font-size: 2.441rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

.info-block h4 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 5px;
}

/* Body Text Fixes With Spacing */
.info-block p {
	padding-bottom: 10px;
	margin-bottom: 10px;
}

/* Link Styles */
.info-block p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.info-block ul,
.info-block ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
	padding-left: 20px;
}
.info-block ul li,
.info-block ol li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 10px;
}
/* Add Space Between Nested Bullets */
.info-block ul ul,
.info-block ul ol,
.info-block ol ul,
.info-block ol ol {
	margin-top: 10px;
	margin-bottom: 10px;
}
/* Styling for Deeper Nested Bullets */
.info-block ul ul ul,
.info-block ul ul ol,
.info-block ul ol ul,
.info-block ul ol ol,
.info-block ol ul ul,
.info-block ol ul ol,
.info-block ol ol ul,
.info-block ol ol ol {
	margin-left: 20px;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
	.info-block p {
	font-size: 1.563rem;
}
}

/* Text Editor Widget */
.legal-content h1 {
	font-size: 3.815rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h2 {
  font-size: 3.052rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h3 {
  font-size: 2.441rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h4 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h5 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

.legal-content h6 {
  font-size: 1.953rem;
	font-weight: 700;
	color: #454545;
	padding-top: 20px;
}

/* Body Text Fixes With Spacing */
.legal-content p {
	padding-bottom: 10px;
	margin-bottom: 10px;
}

/* Link Styles */
.legal-content p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.legal-content ul,
.legal-content ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
	padding-left: 20px;
}
.legal-content ul li,
.legal-content ol li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 10px;
}
/* Add Space Between Nested Bullets */
.legal-content ul ul,
.legal-content ul ol,
.legal-content ol ul,
.legal-content ol ol {
	margin-top: 10px;
	margin-bottom: 10px;
}
/* Styling for deeper nested lists */
.legal-content ul ul ul,
.legal-content ul ul ol,
.legal-content ul ol ul,
.legal-content ul ol ol,
.legal-content ol ul ul,
.legal-content ol ul ol,
.legal-content ol ol ul,
.legal-content ol ol ol {
	margin-left: 20px;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
	.legal-content p {
	font-size: 1.563rem;
}
}

/* Single Post */

/* Header Fixes With Spacing */
.post-toc .elementor-toc__header {
	border: none;
	padding-bottom: 20px;
}

.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  padding-top: 40px;
  font-weight: 700;
  color: #454545;
}

.post-content h2 {
	font-size: 3.052rem;
}

.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  font-size: 1.563rem;
}

/* Body Text Fixes With Spacing */
.post-content p {
	padding-bottom: 10px;
	margin-bottom: 0px;
}

/* Post Link Styles */
.post-content p a {
	color: #454545;
	font-weight: 700;
	text-decoration: underline;
}

/* Bulletlist Item Styles */
.c-post-meta ul li:nth-child(2) {
  font-weight: 400 !important;
}

.post-content ul,
.post-content ol {
	font-size: 1.563rem;
	font-weight: 700;
	padding-bottom: 10px;
}

.post-content ul li {
	font-size: 1.563rem;
	line-height: 1.2;
	margin-bottom: 20px;
}

/* Add Space Between Nested Bullets */
.post-content ul ul {
  margin-top: 10px;
}

.post-content ul ul li {
  margin-bottom: 10px;
}

/* Image Styles With Spacing */
.post-content .wp-block-image img {
  margin: 20px 0;
}

/* Set Mobile Font Sizes */
@media screen and (max-width: 767px) {
  .post-content p {
    font-size: 1.563rem;
  }
}

/*Google logo colors*/
.g-blue{ color:#4285F4; }
.o-red{ color:#DB4437; }
.o-yellow{ color:#F4B400; }
.l-green{ color:#0F9D58; }
.e-red { display:inline block;transform:rotate(-20deg); }

/* Breadcrumb Styles */
.elementor-widget-sp-breadcrumbs .breadcrumb {
    background-color: transparent;
}

.elementor-widget-sp-breadcrumbs .breadcrumb,
.elementor-widget-sp-breadcrumbs .breadcrumb .breadcrumb-item.active {
    color: #F5F8FD;
}

.elementor-widget-sp-breadcrumbs .breadcrumb a {
    color: #F8F8FF;
}

.elementor-widget-sp-breadcrumbs .breadcrumb a:hover {
    color: #FFFFFF;
    text-decoration: underline;
}

/* Table Styles */
.brand-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  color: #232729; /* Black text color */
  background-color: #FFFFFF;
  border: 1px solid #F1EAEA; /* Light Border */
}

.brand-table caption {
  font-weight: bold;
  font-size: 1.2em;
  margin-bottom: 10px;
  text-align: left;
  color: #232729; /* Black */
}

.brand-table th,
.brand-table td {
  padding: 12px 15px;
  text-align: left;
  border: 1px solid #F1EAEA; /* Light Border */
}

.brand-table thead th {
  background-color: #E4342A; /* Main brand color (Red) */
  font-weight: bold;
  color: #FFFFFF; /* White text for better contrast */
  border: 1px solid #E4342A; /* Red border for header cells */
}

.brand-table tbody tr:hover {
  background-color: #F6F7F8; /* Light Background on hover */
}

/* Accent for the first column to highlight repair options */
.brand-table tbody tr td:first-child {
  font-weight: 500; /* Slightly bold */
}

/* Style for Google Maps Containers */
.google-maps-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.static-map {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

/* Make the map responsive on mobile devices */
@media screen and (max-width: 768px) {
    .google-maps-container {
        max-width: 100%;
        margin: 0;
    }
}
/* End Custom Styles */

/* Footer */

/* End Footer */
